<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>貪食蛇大亂鬥：三條命重生模式</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            color: #fff;
        }
        
        .game-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            padding: 20px;
            text-align: center;
        }
        
        .game-header {
            margin-bottom: 20px;
            position: relative;
            z-index: 10;
        }
        
        h1 {
            font-size: 3.5rem;
            margin-bottom: 10px;
            color: #00eeff;
            text-shadow: 0 0 10px rgba(0, 238, 255, 0.7), 
                         0 0 20px rgba(0, 238, 255, 0.5);
            letter-spacing: 2px;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            background: rgba(0, 20, 40, 0.7);
            border: 2px solid rgba(0, 238, 255, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            box-shadow: 0 0 15px rgba(0, 238, 255, 0.2);
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #4fc3f7;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #00eeff;
        }
        
        #gameCanvas {
            background-color: #0d1b2a;
            border-radius: 8px;
            border: 3px solid rgba(0, 238, 255, 0.4);
            box-shadow: 0 0 20px rgba(0, 238, 255, 0.3), 
                        inset 0 0 20px rgba(0, 20, 40, 0.8);
            display: block;
            margin: 0 auto;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        
        button {
            background: linear-gradient(135deg, #00b4db, #0083b0);
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        button:hover {
            background: linear-gradient(135deg, #00eeff, #0083b0);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
        }
        
        button:active {
            transform: translateY(1px);
        }
        
        #restartBtn {
            background: linear-gradient(135deg, #00c853, #009624);
        }
        
        #restartBtn:hover {
            background: linear-gradient(135deg, #00e676, #00c853);
        }
        
        .instructions {
            background: rgba(0, 20, 40, 0.7);
            border-radius: 10px;
            padding: 20px;
            margin-top: 25px;
            border: 2px solid rgba(0, 238, 255, 0.3);
            box-shadow: 0 0 15px rgba(0, 238, 255, 0.2);
            text-align: left;
        }
        
        .instructions h2 {
            color: #4fc3f7;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .instructions ul {
            padding-left: 20px;
            margin: 10px 0;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #e0f7fa;
        }
        
        .instructions p {
            margin-top: 15px;
            text-align: center;
            color: #f44336;
            font-weight: bold;
        }
        
        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(10, 25, 47, 0.95);
            padding: 40px;
            border-radius: 15px;
            border: 3px solid #f44336;
            box-shadow: 0 0 30px rgba(244, 67, 54, 0.6);
            z-index: 100;
            text-align: center;
            display: none;
        }
        
        .game-over h2 {
            font-size: 3rem;
            color: #f44336;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(244, 67, 54, 0.8);
        }
        
        .game-over p {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: #00eeff;
        }
        
        .snake-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }
        
        .player-color {
            background: #2196F3;
        }
        
        .ai1-color {
            background: #E91E63;
        }
        
        .ai2-color {
            background: #9C27B0;
        }
        
        .ai3-color {
            background: #4CAF50;
        }
        
        .lives-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }
        
        .life {
            width: 30px;
            height: 30px;
            background: #f44336;
            border-radius: 50%;
            position: relative;
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.8);
        }
        
        .life::before, .life::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 4px;
            background: #1a1a2e;
            transform: translate(-50%, -50%) rotate(45deg);
        }
        
        .life::after {
            transform: translate(-50%, -50%) rotate(-45deg);
        }
        
        .life.active {
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.8);
        }
        
        .life.active::before, .life.active::after {
            display: none;
        }
        
        .ai-lives-container {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-top: 5px;
        }
        
        .ai-life {
            width: 15px;
            height: 15px;
            background: #f44336;
            border-radius: 50%;
            position: relative;
        }
        
        .ai-life.active {
            background: #4CAF50;
        }
        
        @media (max-width: 600px) {
            h1 {
                font-size: 2.5rem;
            }
            
            .stats {
                flex-direction: column;
                gap: 15px;
            }
            
            .controls {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>貪食蛇大亂鬥</h1>
            <h2 style="color: #FF9800; margin-bottom: 15px;">三條命重生模式</h2>
            
            <div class="snake-legend">
                <div class="legend-item">
                    <div class="legend-color player-color"></div>
                    <span>玩家 (3條命)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color ai1-color"></div>
                    <div>
                        <span>AI 1號 (3條命)</span>
                        <div class="ai-lives-container" id="ai1-lives">
                            <div class="ai-life active"></div>
                            <div class="ai-life active"></div>
                            <div class="ai-life active"></div>
                        </div>
                    </div>
                </div>
                <div class="legend-item">
                    <div class="legend-color ai2-color"></div>
                    <div>
                        <span>AI 2號 (3條命)</span>
                        <div class="ai-lives-container" id="ai2-lives">
                            <div class="ai-life active"></div>
                            <div class="ai-life active"></div>
                            <div class="ai-life active"></div>
                        </div>
                    </div>
                </div>
                <div class="legend-item">
                    <div class="legend-color ai3-color"></div>
                    <div>
                        <span>AI 3號 (3條命)</span>
                        <div class="ai-lives-container" id="ai3-lives">
                            <div class="ai-life active"></div>
                            <div class="ai-life active"></div>
                            <div class="ai-life active"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="lives-container">
                <div class="life active" id="life1"></div>
                <div class="life active" id="life2"></div>
                <div class="life active" id="life3"></div>
            </div>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-label">玩家長度</div>
                    <div id="playerLength" class="stat-value">5</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">食物數量</div>
                    <div id="foodCount" class="stat-value">0</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">存活AI</div>
                    <div id="aiAlive" class="stat-value">3</div>
                </div>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="760" height="500"></canvas>
        
        <div class="controls">
            <button id="startBtn">開始遊戲</button>
            <button id="pauseBtn">暫停</button>
            <button id="restartBtn">重新開始</button>
        </div>
        
        <div class="instructions">
            <h2>遊戲說明</h2>
            <ul>
                <li>使用 <strong>方向鍵</strong> 或 <strong>WASD</strong> 控制你的貪食蛇移動</li>
                <li>吃到食物（黃色點）可以增加長度</li>
                <li>避開三台AI貪食蛇（紅色、紫色、綠色）</li>
                <li>撞到邊界、自己或他人身體都會損失一條生命</li>
                <li>死亡後會在原地生成食物，並在安全位置重生</li>
                <li>玩家和AI都有3條生命，玩家生命耗盡後遊戲結束</li>
                <li>AI死亡後也會重生並繼續遊戲</li>
            </ul>
            <p>小心！重生後有短暫無敵時間（閃爍效果）！</p>
        </div>
        
        <div id="gameOver" class="game-over">
            <h2>遊戲結束！</h2>
            <p id="gameOverMsg">你已經用盡所有生命！</p>
            <button id="playAgainBtn">再玩一次</button>
        </div>
    </div>

    <script>
        // 游戏常量
        const GRID_SIZE = 20;
        const CANVAS_WIDTH = 760;
        const CANVAS_HEIGHT = 500;
        const GRID_WIDTH = CANVAS_WIDTH / GRID_SIZE;
        const GRID_HEIGHT = CANVAS_HEIGHT / GRID_SIZE;
        const TOTAL_CELLS = GRID_WIDTH * GRID_HEIGHT;
        
        // 获取DOM元素
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const restartBtn = document.getElementById('restartBtn');
        const playAgainBtn = document.getElementById('playAgainBtn');
        const gameOverScreen = document.getElementById('gameOver');
        const gameOverMsg = document.getElementById('gameOverMsg');
        const playerLengthElement = document.getElementById('playerLength');
        const foodCountElement = document.getElementById('foodCount');
        const aiAliveElement = document.getElementById('aiAlive');
        const life1 = document.getElementById('life1');
        const life2 = document.getElementById('life2');
        const life3 = document.getElementById('life3');
        const ai1Lives = document.getElementById('ai1-lives');
        const ai2Lives = document.getElementById('ai2-lives');
        const ai3Lives = document.getElementById('ai3-lives');
        
        // 游戏状态
        let gameRunning = false;
        let gamePaused = false;
        let player, aiSnakes, foods;
        let playerLives = 3;
        let playerRespawnTimer = 0;
        let aiAliveCount = 3;
        let playerInvincible = false;
        let invincibleTimer = 0;
        let animationId = null;
        
        // 蛇类
        class Snake {
            constructor(x, y, color, isPlayer = false, id = 0) {
                this.body = [{x, y}];
                this.direction = isPlayer ? 'RIGHT' : ['UP', 'DOWN', 'LEFT', 'RIGHT'][Math.floor(Math.random() * 4)];
                this.color = color;
                this.isPlayer = isPlayer;
                this.grow = false;
                this.growCount = 0;
                this.speed = isPlayer ? 14 : 10 + Math.floor(Math.random() * 5);
                this.moveCounter = 0;
                this.alive = true;
                this.lives = 3;
                this.id = id;
                this.respawnTimer = 0;
                this.invincible = false;
                this.invincibleTimer = 0;
                this.initialX = x;
                this.initialY = y;
            }
            
            // 移动蛇
            move() {
                if (!this.alive) return;
                
                this.moveCounter++;
                if (this.moveCounter < this.speed) return;
                this.moveCounter = 0;
                
                const head = {...this.body[0]};
                
                // 根据方向移动头部
                switch(this.direction) {
                    case 'UP': head.y--; break;
                    case 'DOWN': head.y++; break;
                    case 'LEFT': head.x--; break;
                    case 'RIGHT': head.x++; break;
                }
                
                // 检查边界碰撞
                if (head.x < 0 || head.x >= GRID_WIDTH || head.y < 0 || head.y >= GRID_HEIGHT) {
                    this.die();
                    return;
                }
                
                // 检查自身碰撞（跳过头部）
                for (let i = 1; i < this.body.length; i++) {
                    if (head.x === this.body[i].x && head.y === this.body[i].y) {
                        this.die();
                        return;
                    }
                }
                
                // 移动身体
                this.body.unshift(head);
                if (!this.grow) {
                    this.body.pop();
                } else {
                    this.growCount++;
                    if (this.growCount >= 3) {
                        this.grow = false;
                        this.growCount = 0;
                    }
                }
            }
            
            // 死亡处理
            die() {
                if (!this.alive) return;
                
                this.alive = false;
                this.lives--;
                
                // 将蛇身变成食物
                for (const segment of this.body) {
                    foods.push(new Food(segment.x, segment.y));
                }
                
                // 设置重生计时器
                this.respawnTimer = 60;
            }
            
            // 寻找安全的重生位置
            findSafeRespawnPosition() {
                const maxAttempts = 100;
                for (let attempt = 0; attempt < maxAttempts; attempt++) {
                    const x = Math.floor(Math.random() * GRID_WIDTH);
                    const y = Math.floor(Math.random() * GRID_HEIGHT);
                    
                    // 检查位置是否安全（没有蛇或食物）
                    let safe = true;
                    
                    // 检查所有蛇
                    const allSnakes = [player, ...aiSnakes];
                    for (const snake of allSnakes) {
                        if (snake === this) continue; // 跳过自己
                        
                        for (const segment of snake.body) {
                            if (x === segment.x && y === segment.y) {
                                safe = false;
                                break;
                            }
                        }
                        if (!safe) break;
                    }
                    
                    // 检查食物
                    if (safe) {
                        for (const food of foods) {
                            if (x === food.x && y === food.y) {
                                safe = false;
                                break;
                            }
                        }
                    }
                    
                    if (safe) {
                        return {x, y};
                    }
                }
                
                // 如果找不到安全位置，返回初始位置
                return {x: this.initialX, y: this.initialY};
            }
            
            // 重生
            respawn() {
                if (this.lives <= 0) return;
                
                // 寻找安全的重生位置
                const safePosition = this.findSafeRespawnPosition();
                
                this.body = [{x: safePosition.x, y: safePosition.y}];
                this.direction = this.isPlayer ? 'RIGHT' : ['UP', 'DOWN', 'LEFT', 'RIGHT'][Math.floor(Math.random() * 4)];
                this.alive = true;
                this.invincible = true;
                this.invincibleTimer = 180;
            }
            
            // 绘制蛇
            draw() {
                if (!this.alive) return;
                
                // 绘制身体
                for (let i = 0; i < this.body.length; i++) {
                    const segment = this.body[i];
                    
                    // 无敌时闪烁效果
                    if (this.invincible) {
                        ctx.fillStyle = (Math.floor(Date.now() / 200) % 2 === 0 ? 
                                        'rgba(255, 255, 255, 0.8)' : 
                                        this.color);
                    } else {
                        ctx.fillStyle = this.color;
                    }
                    
                    ctx.fillRect(segment.x * GRID_SIZE, segment.y * GRID_SIZE, GRID_SIZE, GRID_SIZE);
                    
                    // 添加身体内部高光
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                    ctx.fillRect(segment.x * GRID_SIZE + 3, segment.y * GRID_SIZE + 3, GRID_SIZE - 6, GRID_SIZE - 6);
                    
                    // 绘制头部（如果是头部）
                    if (i === 0) {
                        ctx.fillStyle = 'white';
                        ctx.beginPath();
                        
                        // 根据方向绘制眼睛
                        const eyeSize = GRID_SIZE / 5;
                        let eye1X, eye1Y, eye2X, eye2Y;
                        
                        switch(this.direction) {
                            case 'UP':
                                eye1X = segment.x * GRID_SIZE + GRID_SIZE/4;
                                eye1Y = segment.y * GRID_SIZE + GRID_SIZE/3;
                                eye2X = segment.x * GRID_SIZE + 3*GRID_SIZE/4;
                                eye2Y = segment.y * GRID_SIZE + GRID_SIZE/3;
                                break;
                            case 'DOWN':
                                eye1X = segment.x * GRID_SIZE + GRID_SIZE/4;
                                eye1Y = segment.y * GRID_SIZE + 2*GRID_SIZE/3;
                                eye2X = segment.x * GRID_SIZE + 3*GRID_SIZE/4;
                                eye2Y = segment.y * GRID_SIZE + 2*GRID_SIZE/3;
                                break;
                            case 'LEFT':
                                eye1X = segment.x * GRID_SIZE + GRID_SIZE/3;
                                eye1Y = segment.y * GRID_SIZE + GRID_SIZE/4;
                                eye2X = segment.x * GRID_SIZE + GRID_SIZE/3;
                                eye2Y = segment.y * GRID_SIZE + 3*GRID_SIZE/4;
                                break;
                            case 'RIGHT':
                                eye1X = segment.x * GRID_SIZE + 2*GRID_SIZE/3;
                                eye1Y = segment.y * GRID_SIZE + GRID_SIZE/4;
                                eye2X = segment.x * GRID_SIZE + 2*GRID_SIZE/3;
                                eye2Y = segment.y * GRID_SIZE + 3*GRID_SIZE/4;
                                break;
                        }
                        
                        ctx.arc(eye1X, eye1Y, eyeSize, 0, Math.PI * 2);
                        ctx.arc(eye2X, eye2Y, eyeSize, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }
            }
            
            // 改变方向
            changeDirection(newDirection) {
                // 防止直接反向
                if (
                    (this.direction === 'UP' && newDirection === 'DOWN') ||
                    (this.direction === 'DOWN' && newDirection === 'UP') ||
                    (this.direction === 'LEFT' && newDirection === 'RIGHT') ||
                    (this.direction === 'RIGHT' && newDirection === 'LEFT')
                ) {
                    return;
                }
                this.direction = newDirection;
            }
            
            // 检查是否吃到食物
            checkFoodCollision(foods) {
                if (!this.alive) return -1;
                
                const head = this.body[0];
                for (let i = 0; i < foods.length; i++) {
                    if (head.x === foods[i].x && head.y === foods[i].y) {
                        this.grow = true;
                        return i;
                    }
                }
                return -1;
            }
            
            // AI决策
            makeDecision(foods, snakes) {
                if (!this.alive) return;
                
                // 每5帧决策一次
                if (this.moveCounter !== 0) return;
                
                // 寻找最近的食物
                let closestFood = null;
                let minDistance = Infinity;
                
                const head = this.body[0];
                
                for (const food of foods) {
                    const distance = Math.sqrt(Math.pow(food.x - head.x, 2) + Math.pow(food.y - head.y, 2));
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestFood = food;
                    }
                }
                
                if (!closestFood) return;
                
                // 决定移动方向
                const possibleDirections = [];
                
                // 计算食物方向
                const dx = closestFood.x - head.x;
                const dy = closestFood.y - head.y;
                
                // 优先选择靠近食物的方向
                if (Math.abs(dx) > Math.abs(dy)) {
                    if (dx > 0) possibleDirections.push('RIGHT');
                    else possibleDirections.push('LEFT');
                    
                    if (dy > 0) possibleDirections.push('DOWN');
                    else possibleDirections.push('UP');
                } else {
                    if (dy > 0) possibleDirections.push('DOWN');
                    else possibleDirections.push('UP');
                    
                    if (dx > 0) possibleDirections.push('RIGHT');
                    else possibleDirections.push('LEFT');
                }
                
                // 随机添加其他方向增加不可预测性
                if (Math.random() > 0.7) {
                    possibleDirections.push(['UP', 'DOWN', 'LEFT', 'RIGHT'][Math.floor(Math.random() * 4)]);
                }
                
                // 检查每个可能的方向是否安全
                for (const dir of possibleDirections) {
                    if (this.isDirectionSafe(dir, snakes)) {
                        this.direction = dir;
                        return;
                    }
                }
            }
            
            // 检查方向是否安全
            isDirectionSafe(direction, snakes) {
                const head = {...this.body[0]};
                
                // 模拟移动
                switch(direction) {
                    case 'UP': head.y--; break;
                    case 'DOWN': head.y++; break;
                    case 'LEFT': head.x--; break;
                    case 'RIGHT': head.x++; break;
                }
                
                // 检查边界
                if (head.x < 0 || head.x >= GRID_WIDTH || head.y < 0 || head.y >= GRID_HEIGHT) {
                    return false;
                }
                
                // 检查所有蛇的身体（包括自己）
                for (const snake of snakes) {
                    if (!snake.alive) continue;
                    
                    for (let i = 0; i < snake.body.length; i++) {
                        // 如果是自己，跳过头部（因为头部会移动）
                        if (snake === this && i === 0) continue;
                        
                        const segment = snake.body[i];
                        if (head.x === segment.x && head.y === segment.y) {
                            return false;
                        }
                    }
                }
                
                return true;
            }
        }
        
        // 食物类
        class Food {
            constructor(x, y) {
                this.x = x;
                this.y = y;
            }
            
            draw() {
                // 绘制食物（黄色小点）
                ctx.fillStyle = '#FFEB3B';
                ctx.beginPath();
                ctx.arc(
                    this.x * GRID_SIZE + GRID_SIZE/2,
                    this.y * GRID_SIZE + GRID_SIZE/2,
                    GRID_SIZE/3,
                    0,
                    Math.PI * 2
                );
                ctx.fill();
                
                // 添加发光效果
                ctx.fillStyle = 'rgba(255, 235, 59, 0.4)';
                ctx.beginPath();
                ctx.arc(
                    this.x * GRID_SIZE + GRID_SIZE/2,
                    this.y * GRID_SIZE + GRID_SIZE/2,
                    GRID_SIZE/2,
                    0,
                    Math.PI * 2
                );
                ctx.fill();
            }
        }
        
        // 初始化游戏
        function initGame() {
            // 停止当前游戏循环
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            
            // 初始化玩家蛇
            player = new Snake(
                Math.floor(GRID_WIDTH * 0.3),
                Math.floor(GRID_HEIGHT / 2),
                '#2196F3',
                true
            );
            
            // 初始化AI蛇
            aiSnakes = [
                new Snake(Math.floor(GRID_WIDTH * 0.7), Math.floor(GRID_HEIGHT * 0.3), '#E91E63', false, 1),
                new Snake(Math.floor(GRID_WIDTH * 0.7), Math.floor(GRID_HEIGHT * 0.5), '#9C27B0', false, 2),
                new Snake(Math.floor(GRID_WIDTH * 0.7), Math.floor(GRID_HEIGHT * 0.7), '#4CAF50', false, 3)
            ];
            
            // 初始化食物
            foods = [];
            generateFullFood();
            
            // 重置游戏状态
            gameOverScreen.style.display = 'none';
            playerLives = 3;
            life1.className = 'life active';
            life2.className = 'life active';
            life3.className = 'life active';
            playerRespawnTimer = 0;
            playerInvincible = false;
            aiAliveCount = 3;
            
            // 重置AI生命显示
            updateAILivesDisplay();
            updateStats();
            
            // 清空画布
            ctx.fillStyle = '#0d1b2a';
            ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
            drawGrid();
            
            // 绘制初始状态
            for (const food of foods) {
                food.draw();
            }
            player.draw();
            for (const aiSnake of aiSnakes) {
                aiSnake.draw();
            }
        }
        
        // 生成填满画面的食物
        function generateFullFood() {
            // 创建所有可能位置的数组
            const allPositions = [];
            for (let x = 0; x < GRID_WIDTH; x++) {
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    allPositions.push({x, y});
                }
            }
            
            // 移除蛇的位置
            const allSnakes = [player, ...aiSnakes];
            for (const snake of allSnakes) {
                for (const segment of snake.body) {
                    const index = allPositions.findIndex(pos => 
                        pos.x === segment.x && pos.y === segment.y
                    );
                    if (index !== -1) {
                        allPositions.splice(index, 1);
                    }
                }
            }
            
            // 为剩余位置创建食物
            for (const pos of allPositions) {
                foods.push(new Food(pos.x, pos.y));
            }
        }
        
        // 更新游戏状态
        function updateStats() {
            playerLengthElement.textContent = player.body.length;
            foodCountElement.textContent = foods.length;
            
            // 计算存活的AI数量
            aiAliveCount = aiSnakes.filter(snake => snake.lives > 0).length;
            aiAliveElement.textContent = aiAliveCount;
        }
        
        // 更新生命显示
        function updateLivesDisplay() {
            life1.className = playerLives >= 1 ? 'life active' : 'life';
            life2.className = playerLives >= 2 ? 'life active' : 'life';
            life3.className = playerLives >= 3 ? 'life active' : 'life';
        }
        
        // 更新AI生命显示
        function updateAILivesDisplay() {
            updateAISnakeLives(aiSnakes[0], ai1Lives);
            updateAISnakeLives(aiSnakes[1], ai2Lives);
            updateAISnakeLives(aiSnakes[2], ai3Lives);
        }
        
        function updateAISnakeLives(snake, container) {
            const lives = container.querySelectorAll('.ai-life');
            for (let i = 0; i < 3; i++) {
                if (i < snake.lives) {
                    lives[i].className = 'ai-life active';
                } else {
                    lives[i].className = 'ai-life';
                }
            }
        }
        
        // 绘制网格背景
        function drawGrid() {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
            ctx.lineWidth = 0.5;
            
            // 垂直线
            for (let x = 0; x < CANVAS_WIDTH; x += GRID_SIZE) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, CANVAS_HEIGHT);
                ctx.stroke();
            }
            
            // 水平线
            for (let y = 0; y < CANVAS_HEIGHT; y += GRID_SIZE) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(CANVAS_WIDTH, y);
                ctx.stroke();
            }
        }
        
        // 游戏主循环
        function gameLoop() {
            if (!gameRunning || gamePaused) return;
            
            // 清空画布
            ctx.fillStyle = '#0d1b2a';
            ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
            
            // 绘制网格
            drawGrid();
            
            // 检查游戏结束条件 - 玩家生命耗尽 (移到循环开头，优先判断)
            if (playerLives <= 0) {
                // 确保在显示游戏结束画面前，玩家蛇不再活动或绘制
                player.alive = false; 
                gameOverMsg.textContent = "你已經用盡所有生命！";
                endGame();
                return;
            }
            
            // 处理玩家重生
            if (!player.alive && playerLives > 0) { // 确保生命大于0才重生
                player.respawnTimer--;
                if (player.respawnTimer <= 0) {
                    player.respawn();
                    player.respawnTimer = 0;
                }
            }
            
            // 处理玩家无敌计时器
            if (player.alive && player.invincible) {
                player.invincibleTimer--;
                if (player.invincibleTimer <= 0) {
                    player.invincible = false;
                }
            }
            
            // 处理AI蛇
            for (const aiSnake of aiSnakes) {
                // 处理AI重生
                if (!aiSnake.alive && aiSnake.lives > 0) {
                    aiSnake.respawnTimer--;
                    if (aiSnake.respawnTimer <= 0) {
                        aiSnake.respawn();
                        aiSnake.respawnTimer = 0;
                    }
                }
                
                // 处理AI无敌时间
                if (aiSnake.alive && aiSnake.invincible) {
                    aiSnake.invincibleTimer--;
                    if (aiSnake.invincibleTimer <= 0) {
                        aiSnake.invincible = false;
                    }
                }
            }
            
            // 更新玩家蛇
            if (player.alive) {
                player.move();
            }
            
            // 更新AI蛇
            const allSnakes = [player, ...aiSnakes];
            for (const aiSnake of aiSnakes) {
                if (aiSnake.alive) {
                    aiSnake.makeDecision(foods, allSnakes);
                    aiSnake.move();
                }
            }
            
            // 检查玩家是否吃到食物
            if (player.alive) {
                const foodIndex = player.checkFoodCollision(foods);
                if (foodIndex !== -1) {
                    foods.splice(foodIndex, 1);
                }
            }
            
            // 检查AI是否吃到食物
            for (const aiSnake of aiSnakes) {
                if (aiSnake.alive) {
                    const aiFoodIndex = aiSnake.checkFoodCollision(foods);
                    if (aiFoodIndex !== -1) {
                        foods.splice(aiFoodIndex, 1);
                    }
                }
            }
            
            // 检查玩家是否与AI蛇碰撞
            if (player.alive && !player.invincible) {
                for (const aiSnake of aiSnakes) {
                    if (!aiSnake.alive || aiSnake.invincible) continue;
                    
                    for (const segment of aiSnake.body) {
                        if (player.body[0].x === segment.x && player.body[0].y === segment.y) {
                            player.die();
                            playerLives--;
                            player.respawnTimer = 60;
                            updateLivesDisplay();
                            break;
                        }
                    }
                }
            }
            
            // 检查AI是否与玩家或其他AI碰撞
            for (const aiSnake of aiSnakes) {
                if (!aiSnake.alive || aiSnake.invincible) continue;
                
                // 检查与玩家碰撞
                if (player.alive && !player.invincible) {
                    for (const segment of player.body) {
                        if (aiSnake.body[0].x === segment.x && aiSnake.body[0].y === segment.y) {
                            aiSnake.die();
                            updateAILivesDisplay();
                            break;
                        }
                    }
                }
                
                // 检查与其他AI碰撞
                for (const otherAISnake of aiSnakes) {
                    if (aiSnake === otherAISnake || !otherAISnake.alive || otherAISnake.invincible) continue;
                    
                    for (const segment of otherAISnake.body) {
                        if (aiSnake.body[0].x === segment.x && aiSnake.body[0].y === segment.y) {
                            aiSnake.die();
                            updateAILivesDisplay();
                            break;
                        }
                    }
                }
                
                // 检查与自身碰撞（已经在move中处理）
            }
            
            // 绘制食物
            for (const food of foods) {
                food.draw();
            }
            
            // 绘制蛇
            if (player.alive) {
                player.draw();
            }
            for (const aiSnake of aiSnakes) {
                if (aiSnake.alive) {
                    aiSnake.draw();
                }
            }
            
            // 更新游戏状态
            updateStats();
            updateAILivesDisplay();
            
            // 检查游戏结束条件 - 玩家生命耗尽 (原始位置，将被上面的新判斷取代或移除)
            // if (playerLives <= 0 && !player.alive) { // 此行可以註解或刪除
            //     gameOverMsg.textContent = "你已經用盡所有生命！";
            //     endGame();
            //     return;
            // }
            
            // 继续游戏循环
            animationId = requestAnimationFrame(gameLoop);
        }
        
        // 结束游戏
        function endGame() {
            gameRunning = false;
            gameOverScreen.style.display = 'block';
        }
        
        // 事件监听
        startBtn.addEventListener('click', () => {
            if (!gameRunning) {
                gameRunning = true;
                gamePaused = false;
                gameLoop();
                startBtn.textContent = '繼續遊戲';
            } else if (gamePaused) {
                gamePaused = false;
                gameLoop();
                startBtn.textContent = '繼續遊戲';
            }
        });
        
        pauseBtn.addEventListener('click', () => {
            if (gameRunning && !gamePaused) {
                gamePaused = true;
                startBtn.textContent = '繼續遊戲';
            }
        });
        
        restartBtn.addEventListener('click', () => {
            gameRunning = false;
            gamePaused = false;
            initGame();
            startBtn.textContent = '開始遊戲';
        });
        
        playAgainBtn.addEventListener('click', () => {
            gameRunning = false;
            gamePaused = false;
            initGame();
            gameOverScreen.style.display = 'none';
            startBtn.textContent = '開始遊戲';
        });
        
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning || gamePaused || !player.alive) return;
            
            switch(e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    player.changeDirection('UP');
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    player.changeDirection('DOWN');
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    player.changeDirection('LEFT');
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    player.changeDirection('RIGHT');
                    break;
            }
        });
        
        // 初始化并开始游戏
        initGame();
    </script>
</body>
</html>